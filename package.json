{"name": "starter-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "npm run format:lint && npm run format:pretty", "format:lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx --fix --config eslint.config.mjs", "format:pretty": "prettier . --write"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/react": "^1.2.12", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@vercel/blob": "^1.1.1", "@vercel/functions": "^2.2.2", "ai": "^4.3.16", "bcrypt-ts": "^7.1.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "drizzle-orm": "^0.44.2", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.19.2", "lucide-react": "^0.523.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "orderedmap": "^2.1.1", "papaparse": "^5.5.3", "postgres": "^3.4.7", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-data-grid": "7.0.0-beta.47", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-use": "^17.6.0", "redis": "^5.5.6", "remark-gfm": "^4.0.1", "resumable-stream": "^2.2.1", "sonner": "^2.0.5", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint/eslintrc": "^3", "@oneyoung/pino-cli": "^1.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "lint-staged": "^11.1.2", "prettier": "3.0.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "yorkie-pnpm": "^2.0.1"}, "gitHooks": {"commit-msg": "commitlint --edit", "pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,cjs,mjs,ts,tsx}": "eslint --fix --config eslint.config.mjs"}}