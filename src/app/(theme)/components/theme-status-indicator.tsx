'use client'

import { useState } from 'react'
import { Co<PERSON>, Check } from 'lucide-react'
import { useCopyToClipboard } from 'react-use'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useCustomTheme } from '@/hooks/use-custom-theme'
import { ClientOnly } from '@/components/client/client-only'

interface ThemeStatusIndicatorProps {
  showToggle?: boolean
  showCopy?: boolean
  variant?: 'badge' | 'button' | 'full'
  className?: string
}

/**
 * 主题状态指示器组件
 * 显示当前主题状态，可选择性地提供切换功能
 */
export function ThemeStatusIndicator({
  showToggle = false,
  showCopy = false,
  variant = 'badge',
  className = ''
}: ThemeStatusIndicatorProps) {
  const {
    isCustomThemeActive,
    toggleCustomTheme,
    getCurrentHexColor,
    themeConfig,
    generateThemeCSS
  } = useCustomTheme()

  const [, copyToClipboard] = useCopyToClipboard()
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      const css = generateThemeCSS()
      await copyToClipboard(css)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy CSS:', error)
    }
  }

  if (variant === 'button') {
    return (
      <ClientOnly
        fallback={
          <Button
            variant="outline"
            size="sm">
            主题
          </Button>
        }>
        <Button
          variant="outline"
          size="sm"
          onClick={() => toggleCustomTheme(!isCustomThemeActive)}
          className={className}>
          {isCustomThemeActive ? '自定义主题' : '默认主题'}
        </Button>
      </ClientOnly>
    )
  }

  if (variant === 'full') {
    return (
      <ClientOnly
        fallback={
          <div className={`flex items-center gap-2 ${className}`}>
            <Badge variant="secondary">加载中...</Badge>
          </div>
        }>
        <div className={`flex items-center gap-2 ${className}`}>
          {isCustomThemeActive && (
            <div
              className="w-4 h-4 rounded-full border border-border"
              style={{ backgroundColor: getCurrentHexColor() }}
              title={`H:${themeConfig.hue}° S:${themeConfig.saturation}% L:${themeConfig.lightness}%`}
            />
          )}
          <Badge variant={isCustomThemeActive ? 'default' : 'secondary'}>
            {isCustomThemeActive ? '自定义主题' : '默认主题'}
          </Badge>
          {showToggle && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleCustomTheme(!isCustomThemeActive)}>
              {isCustomThemeActive ? '禁用' : '启用'}
            </Button>
          )}
          {showCopy && isCustomThemeActive && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}>
              {copied ? (
                <Check className="w-3 h-3" />
              ) : (
                <Copy className="w-3 h-3" />
              )}
            </Button>
          )}
        </div>
      </ClientOnly>
    )
  }

  // 默认 badge 变体
  return (
    <ClientOnly fallback={<Badge variant="secondary">加载中...</Badge>}>
      <div className={`flex items-center gap-2 ${className}`}>
        {isCustomThemeActive && (
          <div
            className="w-3 h-3 rounded-full border border-border"
            style={{ backgroundColor: getCurrentHexColor() }}
          />
        )}
        <Badge variant={isCustomThemeActive ? 'default' : 'secondary'}>
          {isCustomThemeActive ? '自定义' : '默认'}
        </Badge>
      </div>
    </ClientOnly>
  )
}
