// 自定义 fetch 配置，支持代理

// import { ProxyAgent, setGlobalDispatcher } from 'undici';

// export function setupProxy() {
//   if (process.env.NODE_ENV === 'development' && !process.env.PLAYWRIGHT) {
//     const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;

//     if (proxyUrl) {
//       console.log('🌐 设置 undici 代理:', proxyUrl);

//       const proxyAgent = new ProxyAgent(proxyUrl);
//       setGlobalDispatcher(proxyAgent);
//     }
//   }
// }

// // 在应用启动时调用
// setupProxy();
