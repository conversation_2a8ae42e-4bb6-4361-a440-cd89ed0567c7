'use client'

import { useEffect, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { useCopyToClipboard } from 'react-use'
import {
  type ThemeConfig,
  type CSSFormat,
  generateThemeVariables,
  applyThemeVariables,
  removeCustomTheme,
  hslToHex,
  hexToHsl,
  generateThemeCSS
} from '@/utils/theme'

// 自定义 localStorage hook，确保状态同步
function useLocalStorageState<T>(key: string, defaultValue: T) {
  const [state, setState] = useState<T>(() => {
    if (typeof window === 'undefined') return defaultValue
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  })

  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      setState(prevState => {
        const newValue =
          typeof value === 'function'
            ? (value as (prev: T) => T)(prevState)
            : value
        try {
          window.localStorage.setItem(key, JSON.stringify(newValue))
        } catch (error) {
          console.warn(`Error setting localStorage key "${key}":`, error)
        }
        return newValue
      })
    },
    [key]
  )

  return [state, setValue] as const
}

/**
 * 自定义主题管理Hook
 * 提供主题配置、应用、重置等功能
 */
export function useCustomTheme() {
  const { resolvedTheme } = useTheme()
  const [, copyToClipboard] = useCopyToClipboard()

  // 主题配置状态
  const [themeConfig, setThemeConfig] = useLocalStorageState<ThemeConfig>(
    'custom-theme-config',
    {
      hue: 220,
      saturation: 70, // 默认70%饱和度，更温和
      lightness: 75, // 默认75%亮度，确保是light模式
      radius: 0.5 // 默认0.5rem圆角
    }
  )

  // 自定义主题激活状态
  const [isCustomThemeActive, setIsCustomThemeActive] = useLocalStorageState(
    'custom-theme-active',
    false
  )

  // CSS格式选择状态
  const [cssFormat, setCssFormat] = useLocalStorageState<CSSFormat>(
    'css-format',
    'hsl'
  )

  /**
   * 应用当前主题配置
   */
  const applyTheme = useCallback(() => {
    const variables = generateThemeVariables(
      themeConfig.hue,
      themeConfig.saturation,
      themeConfig.lightness,
      resolvedTheme || 'light',
      themeConfig.radius
    )

    applyThemeVariables(variables, resolvedTheme || 'light')
    setIsCustomThemeActive(true)

    // 触发storage事件，通知其他组件和标签页
    window.dispatchEvent(
      new StorageEvent('storage', {
        key: 'custom-theme-active',
        newValue: 'true',
        storageArea: localStorage
      })
    )
  }, [themeConfig, resolvedTheme, setIsCustomThemeActive])

  /**
   * 重置主题到默认状态
   */
  const resetTheme = useCallback(() => {
    removeCustomTheme()

    setThemeConfig({
      hue: 220,
      saturation: 70,
      lightness: 75,
      radius: 0.5
    })
    setIsCustomThemeActive(false)

    // 触发storage事件，通知其他组件和标签页
    window.dispatchEvent(
      new StorageEvent('storage', {
        key: 'custom-theme-active',
        newValue: 'false',
        storageArea: localStorage
      })
    )
  }, [setThemeConfig, setIsCustomThemeActive])

  /**
   * 更新主题配置
   */
  const updateThemeConfig = useCallback(
    (newConfig: Partial<ThemeConfig>) => {
      setThemeConfig(prevConfig => {
        // 确保所有现有值都被保留，只更新传入的新值
        const updatedConfig: ThemeConfig = {
          hue: newConfig.hue !== undefined ? newConfig.hue : prevConfig.hue,
          saturation:
            newConfig.saturation !== undefined
              ? newConfig.saturation
              : prevConfig.saturation,
          lightness:
            newConfig.lightness !== undefined
              ? newConfig.lightness
              : prevConfig.lightness,
          radius:
            newConfig.radius !== undefined
              ? newConfig.radius
              : prevConfig.radius
        }

        // 如果自定义主题已激活，立即应用更改
        if (isCustomThemeActive) {
          const variables = generateThemeVariables(
            updatedConfig.hue,
            updatedConfig.saturation,
            updatedConfig.lightness,
            resolvedTheme || 'light',
            updatedConfig.radius
          )
          applyThemeVariables(variables, resolvedTheme || 'light')

          // 触发storage事件，通知其他组件和标签页
          window.dispatchEvent(
            new StorageEvent('storage', {
              key: 'custom-theme-config',
              newValue: JSON.stringify(updatedConfig),
              storageArea: localStorage
            })
          )
        }

        return updatedConfig
      })
    },
    [isCustomThemeActive, resolvedTheme]
  )

  /**
   * 从Hex颜色更新主题配置
   */
  const updateFromHexColor = useCallback(
    (hexColor: string) => {
      const [h, s, l] = hexToHsl(hexColor)
      updateThemeConfig({ hue: h, saturation: s, lightness: l })
    },
    [updateThemeConfig]
  )

  /**
   * 设置预设主题
   */
  const setPresetTheme = useCallback(
    (preset: { hue: number; saturation: number; lightness: number }) => {
      updateThemeConfig(preset)
    },
    [updateThemeConfig]
  )

  /**
   * 切换自定义主题状态
   */
  const toggleCustomTheme = (active: boolean) => {
    if (active) {
      applyTheme()
    } else {
      removeCustomTheme()
      setIsCustomThemeActive(false)

      // 触发storage事件，通知其他组件和标签页
      window.dispatchEvent(
        new StorageEvent('storage', {
          key: 'custom-theme-active',
          newValue: 'false',
          storageArea: localStorage
        })
      )
    }
  }

  /**
   * 获取当前主题的Hex颜色值
   */
  const getCurrentHexColor = () => {
    return hslToHex(
      themeConfig.hue,
      themeConfig.saturation,
      themeConfig.lightness
    )
  }

  /**
   * 获取当前主题的OKLCH值
   */
  const getCurrentOklchValues = () => {
    const lightness = themeConfig.lightness / 100
    const chroma = themeConfig.saturation / 100
    const hue = themeConfig.hue

    return [lightness, chroma, hue] as const
  }

  /**
   * 生成当前主题的CSS代码
   */
  const generateCurrentThemeCSS = (format?: CSSFormat) => {
    return generateThemeCSS(
      themeConfig.hue,
      themeConfig.saturation,
      themeConfig.lightness,
      format || cssFormat,
      resolvedTheme || 'light',
      themeConfig.radius
    )
  }

  /**
   * 复制主题CSS到剪贴板
   */
  const copyThemeCSS = async (format?: CSSFormat) => {
    try {
      const css = generateCurrentThemeCSS(format)
      await copyToClipboard(css)
      return true
    } catch (error) {
      console.error('Failed to copy CSS:', error)
      return false
    }
  }

  // 当系统主题改变时重新应用自定义主题
  useEffect(() => {
    if (isCustomThemeActive) {
      const variables = generateThemeVariables(
        themeConfig.hue,
        themeConfig.saturation,
        themeConfig.lightness,
        resolvedTheme || 'light',
        themeConfig.radius
      )
      applyThemeVariables(variables, resolvedTheme || 'light')
    }
  }, [resolvedTheme])

  // 初始化时应用保存的主题
  useEffect(() => {
    if (isCustomThemeActive) {
      const timer = setTimeout(() => {
        const variables = generateThemeVariables(
          themeConfig.hue,
          themeConfig.saturation,
          themeConfig.lightness,
          resolvedTheme || 'light',
          themeConfig.radius
        )
        applyThemeVariables(variables, resolvedTheme || 'light')
      }, 100) // 延迟确保DOM已准备好

      return () => clearTimeout(timer)
    }
  }, [])

  return {
    // 状态
    themeConfig,
    isCustomThemeActive,
    cssFormat,

    // 操作方法
    applyTheme,
    resetTheme,
    updateThemeConfig,
    updateFromHexColor,
    setPresetTheme,
    toggleCustomTheme,
    setCssFormat,

    // 计算值
    getCurrentHexColor,
    getCurrentOklchValues,
    generateThemeCSS: generateCurrentThemeCSS,
    copyThemeCSS
  }
}
