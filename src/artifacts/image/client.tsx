import { Artifact } from '@/components/create-artifact'
import { CopyIcon, RedoIcon, UndoIcon } from '@/components/icons'
import { ImageEditor } from '@/components/image-editor'
import { copyImageToClipboard } from '@/utils/copy'
import { toast } from 'sonner'

export const imageArtifact = new Artifact({
  kind: 'image',
  description: 'Useful for image generation',
  onStreamPart: ({ streamPart, setArtifact }) => {
    if (streamPart.type === 'image-delta') {
      setArtifact(draftArtifact => ({
        ...draftArtifact,
        content: streamPart.content as string,
        isVisible: true,
        status: 'streaming'
      }))
    }
  },
  content: ImageEditor,
  actions: [
    {
      icon: <UndoIcon size={18} />,
      description: 'View Previous version',
      onClick: ({ handleVersionChange }) => {
        handleVersionChange('prev')
      },
      isDisabled: ({ currentVersionIndex }) => {
        if (currentVersionIndex === 0) {
          return true
        }

        return false
      }
    },
    {
      icon: <RedoIcon size={18} />,
      description: 'View Next version',
      onClick: ({ handleVersionChange }) => {
        handleVersionChange('next')
      },
      isDisabled: ({ isCurrentVersion }) => {
        if (isCurrentVersion) {
          return true
        }

        return false
      }
    },
    {
      icon: <CopyIcon size={18} />,
      description: 'Copy image to clipboard',
      onClick: async ({ content }) => {
        try {
          const success = await copyImageToClipboard(content)
          if (success) {
            toast.success('Copied image to clipboard!')
          } else {
            toast.error('Failed to copy image to clipboard')
          }
        } catch (error) {
          console.error('Failed to copy image:', error)
          toast.error('Failed to copy image to clipboard')
        }
      }
    }
  ],
  toolbar: []
})
