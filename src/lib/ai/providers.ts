import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel
} from 'ai'
// import { xai } from '@ai-sdk/xai';
import { deepseek } from '@ai-sdk/deepseek'
import { isTestEnvironment } from '../constants'
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel
} from './models.test'

// const xaiProvider = customProvider({
//   languageModels: {
//     'chat-model': xai('grok-2-vision-1212'),
//     'chat-model-reasoning': wrapLanguageModel({
//       model: xai('grok-3-mini-beta'),
//       middleware: extractReasoningMiddleware({ tagName: 'think' }),
//     }),
//     'title-model': xai('grok-2-1212'),
//     'artifact-model': xai('grok-2-1212'),
//   },
//   imageModels: {
//     'small-model': xai.image('grok-2-image'),
//   },
// });

// https://ai-sdk.dev/providers/ai-sdk-providers/deepseek
const deepseekProvider = customProvider({
  languageModels: {
    'chat-model': deepseek('deepseek-chat'),
    'chat-model-reasoning': wrapLanguageModel({
      model: deepseek('deepseek-reasoner'),
      middleware: extractReasoningMiddleware({ tagName: 'think' })
    }),
    'title-model': deepseek('deepseek-chat')
  }
  // deepseek不支持图片生成
  // imageModels: {
  //   'small-model': deepseek('deepseek-chat'),
  // },
})

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel
      }
    })
  : deepseekProvider
