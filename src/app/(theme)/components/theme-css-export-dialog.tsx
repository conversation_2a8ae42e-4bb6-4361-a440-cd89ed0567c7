'use client'

import { useState, useEffect, useCallback } from 'react'
import { Copy, Check, Download, ChevronDown, Code } from 'lucide-react'
import { useCopyToClipboard } from 'react-use'
import { useTheme } from 'next-themes'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useCustomTheme } from '@/hooks/use-custom-theme'
import { ClientOnly } from '@/components/client/client-only'
import {
  loadThemeConfig,
  generateThemeCSS,
  isCustomThemeActive,
  type CSSFormat,
  type ThemeConfig
} from '@/utils/theme'

/**
 * 主题CSS导出对话框组件
 * 在弹窗中显示生成的CSS代码并提供复制功能
 */
export function ThemeCSSExportDialog() {
  const { resolvedTheme } = useTheme()
  const {
    isCustomThemeActive: hookIsActive,
    cssFormat: hookCssFormat,
    setCssFormat
  } = useCustomTheme()
  const [, copyToClipboard] = useCopyToClipboard()
  const [copied, setCopied] = useState(false)
  const [open, setOpen] = useState(false)

  // 实时从 localStorage 读取最新状态，确保数据同步
  const [currentThemeConfig, setCurrentThemeConfig] = useState<ThemeConfig>({
    hue: 220,
    saturation: 70,
    lightness: 75,
    radius: 0.5
  })
  const [currentCssFormat, setCurrentCssFormat] = useState<CSSFormat>('hsl')
  const [currentIsActive, setCurrentIsActive] = useState(false)

  const formatLabels: Record<CSSFormat, string> = {
    hsl: 'HSL 格式',
    oklch: 'OKLCH 格式'
  }

  // 从 localStorage 读取最新状态的函数
  const refreshCurrentState = useCallback(() => {
    if (typeof window === 'undefined') return

    const themeConfig = loadThemeConfig()
    const isActive = isCustomThemeActive()
    const cssFormat = (localStorage.getItem('css-format') as CSSFormat) || 'hsl'

    if (themeConfig) {
      setCurrentThemeConfig(themeConfig)
    }
    setCurrentIsActive(isActive)
    setCurrentCssFormat(cssFormat)
  }, [])

  // 组件挂载和对话框打开时刷新状态
  useEffect(() => {
    refreshCurrentState()
  }, [refreshCurrentState])

  // 对话框打开时刷新状态
  useEffect(() => {
    if (open) {
      refreshCurrentState()
    }
  }, [open, refreshCurrentState])

  // 监听 localStorage 变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'custom-theme-config' ||
          e.key === 'custom-theme-active' ||
          e.key === 'css-format') {
        refreshCurrentState()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [refreshCurrentState])

  // 生成当前CSS代码
  const getCurrentCSS = useCallback(() => {
    return generateThemeCSS(
      currentThemeConfig.hue,
      currentThemeConfig.saturation,
      currentThemeConfig.lightness,
      currentCssFormat,
      resolvedTheme || 'light',
      currentThemeConfig.radius
    )
  }, [currentThemeConfig, currentCssFormat, resolvedTheme])

  const handleCopy = async () => {
    try {
      const css = getCurrentCSS()
      await copyToClipboard(css)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy CSS:', error)
    }
  }

  const handleDownload = () => {
    const css = getCurrentCSS()
    const blob = new Blob([css], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `custom-theme-${currentCssFormat}.css`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleFormatChange = (format: CSSFormat) => {
    setCssFormat(format) // 更新 hook 状态
    setCurrentCssFormat(format) // 立即更新本地状态
    localStorage.setItem('css-format', format) // 直接更新 localStorage
  }



  return (
    <ClientOnly
      fallback={
        <Button disabled>
          <Code className="w-4 h-4 mr-2" />
          导出 CSS
        </Button>
      }>
      <Dialog
        open={open}
        onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button disabled={!hookIsActive}>
            <Code className="w-4 h-4 mr-2" />
            导出 CSS
          </Button>
        </DialogTrigger>

        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader className="pr-8">
            <DialogTitle className="flex items-center justify-between">
              <span>CSS 代码导出</span>
              <div className="flex items-center gap-2">
                <Badge variant={currentIsActive ? 'default' : 'secondary'}>
                  {currentIsActive ? '自定义主题' : '默认主题'}
                </Badge>
                {currentIsActive && (
                  <Badge variant="outline">{formatLabels[currentCssFormat]}</Badge>
                )}
              </div>
            </DialogTitle>
            <DialogDescription>
              复制或下载生成的CSS代码，可直接用于其他项目
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            {currentIsActive ? (
              <>
                {/* 主题信息和格式选择 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>色相: {currentThemeConfig.hue}°</span>
                    <span>饱和度: {currentThemeConfig.saturation}%</span>
                    <span>亮度: {currentThemeConfig.lightness}%</span>
                    <span>圆角: {currentThemeConfig.radius}rem</span>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm">
                        {formatLabels[currentCssFormat]}
                        <ChevronDown className="w-3 h-3 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleFormatChange('hsl')}
                        className={
                          currentCssFormat === 'hsl'
                            ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                            : ''
                        }>
                        HSL 格式
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleFormatChange('oklch')}
                        className={
                          currentCssFormat === 'oklch'
                            ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                            : ''
                        }>
                        OKLCH 格式
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  <Button
                    onClick={handleCopy}
                    className="flex-1"
                    variant={copied ? 'default' : 'outline'}>
                    {copied ? (
                      <>
                        <Check className="w-4 h-4 mr-2" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-2" />
                        复制 CSS
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleDownload}
                    variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    下载
                  </Button>
                </div>

                {/* CSS 代码预览 */}
                <div className="flex-1 overflow-hidden flex flex-col space-y-2 min-h-0">
                  <h4 className="text-sm font-medium">CSS 代码预览</h4>
                  <div className="relative flex-1 min-h-0 border rounded-lg overflow-auto">
                    <pre className="bg-muted p-4 rounded-lg text-xs h-full w-full whitespace-pre-wrap break-words">
                      <code>{getCurrentCSS()}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute top-2 right-2 bg-background/80 hover:bg-background"
                      onClick={handleCopy}>
                      {copied ? (
                        <Check className="w-3 h-3" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* 使用说明 */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">使用说明</h4>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>1. 选择您偏好的CSS颜色格式（HSL 或 OKLCH）</p>
                    <p>2. 复制上面的CSS代码</p>
                    <p>3. 将代码添加到您的项目的CSS文件中</p>
                    <p>4. 确保您的项目支持CSS自定义属性（CSS Variables）</p>
                    <p>5. 代码包含浅色和深色两种主题变体</p>
                    {currentCssFormat === 'oklch' && (
                      <p className="text-amber-600 dark:text-amber-400">
                        ⚠️ OKLCH格式需要较新的浏览器支持
                      </p>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p className="mb-4">请先启用自定义主题以生成CSS代码</p>
                <p className="text-xs">您可以在主题设置中配置自定义颜色</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </ClientOnly>
  )
}
