'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, Quote } from 'lucide-react'

export default function Testimonial() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const testimonials = [
    {
      name: '张伟',
      role: '技术总监',
      company: '创新科技有限公司',
      avatar: '/api/placeholder/64/64',
      rating: 5,
      content:
        '这个平台彻底改变了我们的开发流程。从原型到生产环境的部署时间缩短了 70%，团队效率显著提升。',
      highlight: '部署时间缩短 70%'
    },
    {
      name: '李小明',
      role: '产品经理',
      company: '数字化解决方案公司',
      avatar: '/api/placeholder/64/64',
      rating: 5,
      content:
        '用户界面设计非常直观，即使是非技术人员也能快速上手。客户满意度提升了 40%。',
      highlight: '客户满意度提升 40%'
    },
    {
      name: '王芳',
      role: 'CEO',
      company: '初创企业',
      avatar: '/api/placeholder/64/64',
      rating: 5,
      content:
        '作为一家初创公司，我们需要快速迭代和部署。这个平台让我们能够专注于核心业务，而不是基础设施。',
      highlight: '专注核心业务'
    },
    {
      name: '陈建国',
      role: '运维工程师',
      company: '大型互联网公司',
      avatar: '/api/placeholder/64/64',
      rating: 5,
      content:
        '监控和分析功能非常强大，帮助我们提前发现和解决问题。系统稳定性提升了 95%。',
      highlight: '系统稳定性提升 95%'
    },
    {
      name: '刘敏',
      role: '开发团队负责人',
      company: '金融科技公司',
      avatar: '/api/placeholder/64/64',
      rating: 5,
      content:
        '安全性是我们最关心的问题，这个平台的多层安全防护让我们完全放心。合规性检查也很完善。',
      highlight: '企业级安全保障'
    }
  ]

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial(prev => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [testimonials.length])

  return (
    <section
      id="testimonials"
      className="py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            客户
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              真实评价
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            听听我们的客户怎么说，他们的成功就是我们最好的证明。
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
              10,000+
            </div>
            <div className="text-muted-foreground">满意客户</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
              4.9/5
            </div>
            <div className="text-muted-foreground">平均评分</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
              99.9%
            </div>
            <div className="text-muted-foreground">系统可用性</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
              24/7
            </div>
            <div className="text-muted-foreground">技术支持</div>
          </div>
        </div>

        {/* Main testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <Card className="relative overflow-hidden">
            <CardContent className="p-8 md:p-12">
              <Quote className="absolute top-6 left-6 w-8 h-8 text-primary/20" />

              <div className="relative">
                <Badge
                  variant="secondary"
                  className="mb-6">
                  {testimonials[currentTestimonial].highlight}
                </Badge>

                <blockquote className="text-xl md:text-2xl leading-relaxed mb-8">
                  {`"${testimonials[currentTestimonial].content}"`}
                </blockquote>

                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-bold text-lg">
                    {testimonials[currentTestimonial].name.charAt(0)}
                  </div>

                  <div className="flex-1">
                    <div className="font-semibold text-lg">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="text-muted-foreground">
                      {testimonials[currentTestimonial].role} ·{' '}
                      {testimonials[currentTestimonial].company}
                    </div>
                    <div className="flex items-center gap-1 mt-1">
                      {[...Array(testimonials[currentTestimonial].rating)].map(
                        (_, i) => (
                          <Star
                            key={i}
                            className="w-4 h-4 fill-primary text-primary"
                          />
                        )
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Testimonial indicators */}
        <div className="flex justify-center gap-2 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentTestimonial
                  ? 'bg-primary'
                  : 'bg-muted-foreground/30'
              }`}
              onClick={() => setCurrentTestimonial(index)}
            />
          ))}
        </div>

        {/* All testimonials grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <Card
              key={index}
              className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 fill-primary text-primary"
                    />
                  ))}
                </div>

                <p className="text-muted-foreground mb-4 line-clamp-3">
                  {`"${testimonial.content}"`}
                </p>

                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-bold">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <div className="font-medium text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {testimonial.company}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
