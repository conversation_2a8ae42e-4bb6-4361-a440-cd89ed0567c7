import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Zap,
  Shield,
  Globe,
  Smartphone,
  BarChart3,
  Users,
  Clock,
  Sparkles
} from 'lucide-react'

export default function Feature() {
  const features = [
    {
      icon: Zap,
      title: '极速性能',
      description:
        '采用最新技术栈，确保应用程序运行速度快如闪电，提供卓越的用户体验。',
      color: 'text-yellow-500'
    },
    {
      icon: Shield,
      title: '安全可靠',
      description: '企业级安全保障，多层加密防护，确保您的数据安全无忧。',
      color: 'text-green-500'
    },
    {
      icon: Globe,
      title: '全球部署',
      description:
        '支持全球多地区部署，CDN 加速，让您的用户无论身在何处都能享受快速服务。',
      color: 'text-blue-500'
    },
    {
      icon: Smartphone,
      title: '移动优先',
      description: '响应式设计，完美适配各种设备，移动端体验与桌面端同样出色。',
      color: 'text-purple-500'
    },
    {
      icon: BarChart3,
      title: '数据分析',
      description:
        '内置强大的数据分析工具，实时监控业务指标，助您做出明智决策。',
      color: 'text-orange-500'
    },
    {
      icon: Users,
      title: '团队协作',
      description: '支持多人协作，权限管理，让团队工作更高效，沟通更顺畅。',
      color: 'text-pink-500'
    },
    {
      icon: Clock,
      title: '24/7 支持',
      description:
        '全天候技术支持，专业团队随时为您解决问题，确保业务不间断运行。',
      color: 'text-red-500'
    },
    {
      icon: Sparkles,
      title: 'AI 驱动',
      description: '集成人工智能技术，自动化处理重复任务，让您专注于核心业务。',
      color: 'text-cyan-500'
    }
  ]

  return (
    <section
      id="features"
      className="py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            强大功能，
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              一应俱全
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            我们提供全面的功能套件，帮助您构建现代化的应用程序，
            从开发到部署，从安全到分析，一站式解决所有需求。
          </p>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-muted/50 hover:border-primary/20">
                <CardHeader className="pb-4">
                  <div
                    className={`w-12 h-12 rounded-lg bg-background border flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`w-6 h-6 ${feature.color}`} />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-4">还有更多功能等您探索</p>
          <div className="flex justify-center">
            <div className="flex -space-x-1">
              {[1, 2, 3].map(i => (
                <div
                  key={i}
                  className="w-2 h-2 rounded-full bg-primary/30 animate-pulse"
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
