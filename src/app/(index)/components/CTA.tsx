import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowRight, Rocket, Star, Users, Zap } from 'lucide-react'

export default function CTA() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main CTA */}
        <Card className="relative overflow-hidden border-primary/20 shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10" />

          <CardContent className="relative p-8 md:p-16 text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
              <Rocket className="w-4 h-4" />
              <span>立即开始您的数字化之旅</span>
            </div>

            {/* Main heading */}
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              准备好
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                改变世界
              </span>
              了吗？
            </h2>

            {/* Subtitle */}
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              加入数万名开发者和企业的行列，使用我们的平台构建下一代应用程序。
              14 天免费试用，无需信用卡，随时可以取消。
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link href="/register">
                <Button
                  size="lg"
                  className="text-lg px-8 py-6 h-auto">
                  免费开始试用
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 py-6 h-auto">
                  联系销售团队
                </Button>
              </Link>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>10,000+ 活跃用户</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4" />
                <span>4.9/5 用户评分</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                <span>99.9% 系统可用性</span>
              </div>
            </div>
          </CardContent>

          {/* Floating elements */}
          <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
          <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-secondary/10 rounded-full blur-xl" />
        </Card>

        {/* Secondary CTAs */}
        <div className="grid md:grid-cols-3 gap-6 mt-16">
          <Card className="text-center p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
              <Rocket className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">快速开始</h3>
            <p className="text-muted-foreground text-sm mb-4">
              5 分钟内完成设置，立即开始构建您的应用程序
            </p>
            <Link href="/docs">
              <Button
                variant="outline"
                size="sm"
                className="w-full">
                查看文档
              </Button>
            </Link>
          </Card>

          <Card className="text-center p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
              <Users className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">加入社区</h3>
            <p className="text-muted-foreground text-sm mb-4">
              与其他开发者交流经验，获得帮助和灵感
            </p>
            <Link href="/community">
              <Button
                variant="outline"
                size="sm"
                className="w-full">
                加入社区
              </Button>
            </Link>
          </Card>

          <Card className="text-center p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
              <Star className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">获得支持</h3>
            <p className="text-muted-foreground text-sm mb-4">
              专业的技术支持团队随时为您提供帮助
            </p>
            <Link href="/support">
              <Button
                variant="outline"
                size="sm"
                className="w-full">
                联系支持
              </Button>
            </Link>
          </Card>
        </div>

        {/* Bottom note */}
        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground">
            无需信用卡 • 14 天免费试用 • 随时可以取消
          </p>
        </div>
      </div>
    </section>
  )
}
