'use client'

import { Moon, Sun } from 'lucide-react'
import { useTheme } from 'next-themes'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ThemeControls } from '../components/theme-controls'
import { StylePreview } from '../components/theme-demo'
import { ClientOnly } from '@/components/client/client-only'

function ThemeContent() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="text-center flex-1 space-y-4">
              <h1 className="text-4xl font-bold tracking-tight">
                shadcn/ui 主题生成器
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                从单一颜色轻松创建自定义主题，可复制粘贴到您的应用中
              </p>
            </div>

            {/* 主题切换按钮 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="h-9 w-9">
                {theme === 'dark' ? (
                  <Sun className="h-4 w-4" />
                ) : (
                  <Moon className="h-4 w-4" />
                )}
                <span className="sr-only">切换主题</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 space-y-8">
        {/* Theme Controls Section */}
        <div>
          <ThemeControls />
        </div>

        {/* Style Preview Section */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>样式预览</CardTitle>
              <CardDescription>查看主题在各种组件中的实际效果</CardDescription>
            </CardHeader>
            <CardContent>
              <StylePreview />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function Theme() {
  return (
    <ClientOnly
      fallback={
        <div className="min-h-screen bg-background">
          <div className="border-b bg-background/95 backdrop-blur">
            <div className="container mx-auto px-6 py-8">
              <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold tracking-tight">
                  shadcn/ui 主题生成器
                </h1>
                <p className="text-xl text-muted-foreground">正在加载...</p>
              </div>
            </div>
          </div>
          <div className="container mx-auto px-6 py-8 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>主题配置</CardTitle>
                <CardDescription>正在加载...</CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>样式预览</CardTitle>
                <CardDescription>正在加载...</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      }>
      <ThemeContent />
    </ClientOnly>
  )
}
