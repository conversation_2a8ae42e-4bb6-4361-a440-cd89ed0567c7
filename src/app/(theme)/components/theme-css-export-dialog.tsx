'use client'

import { useState } from 'react'
import { Copy, Check, Download, ChevronDown, Code } from 'lucide-react'
import { useCopyToClipboard } from 'react-use'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useCustomTheme } from '@/hooks/use-custom-theme'
import { ClientOnly } from '@/components/client/client-only'
import { type CSSFormat } from '@/utils/theme'

/**
 * 主题CSS导出对话框组件
 * 在弹窗中显示生成的CSS代码并提供复制功能
 */
export function ThemeCSSExportDialog() {
  const {
    generateThemeCSS,
    isCustomThemeActive,
    themeConfig,
    cssFormat,
    setCssFormat
  } = useCustomTheme()
  const [, copyToClipboard] = useCopyToClipboard()
  const [copied, setCopied] = useState(false)
  const [open, setOpen] = useState(false)

  const formatLabels: Record<CSSFormat, string> = {
    hsl: 'HSL 格式',
    oklch: 'OKLCH 格式'
  }

  const handleCopy = async () => {
    try {
      const css = generateThemeCSS()
      await copyToClipboard(css)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy CSS:', error)
    }
  }

  const handleDownload = () => {
    const css = generateThemeCSS()
    const blob = new Blob([css], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `custom-theme-${cssFormat}.css`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <ClientOnly
      fallback={
        <Button disabled>
          <Code className="w-4 h-4 mr-2" />
          导出 CSS
        </Button>
      }>
      <Dialog
        open={open}
        onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button disabled={!isCustomThemeActive}>
            <Code className="w-4 h-4 mr-2" />
            导出 CSS
          </Button>
        </DialogTrigger>

        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader className="pr-8">
            <DialogTitle className="flex items-center justify-between">
              <span>CSS 代码导出</span>
              <div className="flex items-center gap-2">
                <Badge variant={isCustomThemeActive ? 'default' : 'secondary'}>
                  {isCustomThemeActive ? '自定义主题' : '默认主题'}
                </Badge>
                {isCustomThemeActive && (
                  <Badge variant="outline">{formatLabels[cssFormat]}</Badge>
                )}
              </div>
            </DialogTitle>
            <DialogDescription>
              复制或下载生成的CSS代码，可直接用于其他项目
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            {isCustomThemeActive ? (
              <>
                {/* 主题信息和格式选择 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>色相: {themeConfig.hue}°</span>
                    <span>饱和度: {themeConfig.saturation}%</span>
                    <span>亮度: {themeConfig.lightness}%</span>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm">
                        {formatLabels[cssFormat]}
                        <ChevronDown className="w-3 h-3 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => setCssFormat('hsl')}
                        className={
                          cssFormat === 'hsl'
                            ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                            : ''
                        }>
                        HSL 格式
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setCssFormat('oklch')}
                        className={
                          cssFormat === 'oklch'
                            ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                            : ''
                        }>
                        OKLCH 格式
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  <Button
                    onClick={handleCopy}
                    className="flex-1"
                    variant={copied ? 'default' : 'outline'}>
                    {copied ? (
                      <>
                        <Check className="w-4 h-4 mr-2" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-2" />
                        复制 CSS
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleDownload}
                    variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    下载
                  </Button>
                </div>

                {/* CSS 代码预览 */}
                <div className="flex-1 overflow-hidden flex flex-col space-y-2 min-h-0">
                  <h4 className="text-sm font-medium">CSS 代码预览</h4>
                  <div className="relative flex-1 min-h-0 border rounded-lg overflow-auto">
                    <pre className="bg-muted p-4 rounded-lg text-xs h-full w-full whitespace-pre-wrap break-words">
                      <code>{generateThemeCSS()}</code>
                    </pre>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute top-2 right-2 bg-background/80 hover:bg-background"
                      onClick={handleCopy}>
                      {copied ? (
                        <Check className="w-3 h-3" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* 使用说明 */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">使用说明</h4>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>1. 选择您偏好的CSS颜色格式（HSL 或 OKLCH）</p>
                    <p>2. 复制上面的CSS代码</p>
                    <p>3. 将代码添加到您的项目的CSS文件中</p>
                    <p>4. 确保您的项目支持CSS自定义属性（CSS Variables）</p>
                    <p>5. 代码包含浅色和深色两种主题变体</p>
                    {cssFormat === 'oklch' && (
                      <p className="text-amber-600 dark:text-amber-400">
                        ⚠️ OKLCH格式需要较新的浏览器支持
                      </p>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p className="mb-4">请先启用自定义主题以生成CSS代码</p>
                <p className="text-xs">您可以在主题设置中配置自定义颜色</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </ClientOnly>
  )
}
