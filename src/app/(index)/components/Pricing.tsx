'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, X, Zap, Crown, Building } from 'lucide-react'

export default function Pricing() {
  const [isAnnual, setIsAnnual] = useState(false)

  const plans = [
    {
      name: '个人版',
      icon: Zap,
      description: '适合个人开发者和小型项目',
      monthlyPrice: 0,
      annualPrice: 0,
      popular: false,
      features: [
        { name: '最多 3 个项目', included: true },
        { name: '5GB 存储空间', included: true },
        { name: '基础技术支持', included: true },
        { name: '社区论坛访问', included: true },
        { name: '基础分析报告', included: true },
        { name: '高级功能', included: false },
        { name: '优先技术支持', included: false },
        { name: '自定义域名', included: false }
      ]
    },
    {
      name: '专业版',
      icon: Crown,
      description: '适合成长中的团队和企业',
      monthlyPrice: 29,
      annualPrice: 290,
      popular: true,
      features: [
        { name: '无限项目', included: true },
        { name: '100GB 存储空间', included: true },
        { name: '优先技术支持', included: true },
        { name: '高级分析报告', included: true },
        { name: '自定义域名', included: true },
        { name: '团队协作工具', included: true },
        { name: 'API 访问权限', included: true },
        { name: '白标解决方案', included: false }
      ]
    },
    {
      name: '企业版',
      icon: Building,
      description: '适合大型企业和组织',
      monthlyPrice: 99,
      annualPrice: 990,
      popular: false,
      features: [
        { name: '无限项目', included: true },
        { name: '1TB 存储空间', included: true },
        { name: '24/7 专属支持', included: true },
        { name: '高级分析报告', included: true },
        { name: '自定义域名', included: true },
        { name: '白标解决方案', included: true },
        { name: 'SSO 单点登录', included: true },
        { name: '专属客户经理', included: true }
      ]
    }
  ]

  const getPrice = (plan: (typeof plans)[0]) => {
    if (plan.monthlyPrice === 0) return '免费'
    const price = isAnnual ? plan.annualPrice : plan.monthlyPrice
    const period = isAnnual ? '年' : '月'
    return `¥${price}/${period}`
  }

  const getSavings = (plan: (typeof plans)[0]) => {
    if (plan.monthlyPrice === 0) return null
    const monthlyCost = plan.monthlyPrice * 12
    const savings = monthlyCost - plan.annualPrice
    const percentage = Math.round((savings / monthlyCost) * 100)
    return percentage
  }

  return (
    <section
      id="pricing"
      className="py-16 md:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            选择适合您的
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              定价方案
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            无论您是个人开发者还是大型企业，我们都有适合您的方案。
            所有方案都包含 14 天免费试用。
          </p>

          {/* Billing toggle */}
          <div className="flex items-center justify-center gap-4">
            <span
              className={`text-sm ${
                !isAnnual ? 'text-foreground' : 'text-muted-foreground'
              }`}>
              按月付费
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-primary' : 'bg-muted-foreground/30'
              }`}>
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span
              className={`text-sm ${
                isAnnual ? 'text-foreground' : 'text-muted-foreground'
              }`}>
              按年付费
            </span>
            {isAnnual && (
              <Badge
                variant="secondary"
                className="ml-2">
                节省 20%
              </Badge>
            )}
          </div>
        </div>

        {/* Pricing cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => {
            const Icon = plan.icon
            const savings = getSavings(plan)

            return (
              <Card
                key={index}
                className={`relative overflow-hidden ${
                  plan.popular
                    ? 'border-primary shadow-lg scale-105'
                    : 'border-muted/50'
                }`}>
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-primary text-primary-foreground text-center py-2 text-sm font-medium">
                    最受欢迎
                  </div>
                )}

                <CardHeader
                  className={`text-center ${plan.popular ? 'pt-12' : 'pt-6'}`}>
                  <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>

                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <p className="text-muted-foreground text-sm">
                    {plan.description}
                  </p>

                  <div className="mt-6">
                    <div className="text-4xl font-bold">{getPrice(plan)}</div>
                    {isAnnual && savings && (
                      <div className="text-sm text-muted-foreground mt-1">
                        节省 {savings}%
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <Link href="/register">
                    <Button
                      className="w-full"
                      variant={plan.popular ? 'default' : 'outline'}>
                      {plan.monthlyPrice === 0 ? '免费开始' : '开始免费试用'}
                    </Button>
                  </Link>

                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center gap-3">
                        {feature.included ? (
                          <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                        ) : (
                          <X className="w-4 h-4 text-muted-foreground/50 flex-shrink-0" />
                        )}
                        <span
                          className={`text-sm ${
                            feature.included
                              ? 'text-foreground'
                              : 'text-muted-foreground/70'
                          }`}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* FAQ link */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-4">
            还有疑问？查看我们的常见问题解答
          </p>
          <Button
            variant="outline"
            asChild>
            <Link href="#faq">查看 FAQ</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
