/**
 * 主题工具函数
 * 基于OKLCH颜色系统的主题生成和管理
 */

export interface ThemeConfig {
  hue: number
  saturation: number
  lightness: number
  radius: number // 0-1.5 (rem)
}

/**
 * 将HSL转换为OKLCH的科学版本
 * 使用更精确的颜色空间转换算法
 */
export function hslToOklch(
  h: number,
  s: number,
  l: number
): [number, number, number] {
  // 将HSL转换为RGB (0-1范围)
  const sNorm = s / 100
  const lNorm = l / 100
  const hNorm = h / 360

  const c = (1 - Math.abs(2 * lNorm - 1)) * sNorm
  const x = c * (1 - Math.abs(((hNorm * 6) % 2) - 1))
  const m = lNorm - c / 2

  let r = 0,
    g = 0,
    b = 0

  if (hNorm * 6 >= 0 && hNorm * 6 < 1) {
    r = c
    g = x
    b = 0
  } else if (hNorm * 6 >= 1 && hNorm * 6 < 2) {
    r = x
    g = c
    b = 0
  } else if (hNorm * 6 >= 2 && hNorm * 6 < 3) {
    r = 0
    g = c
    b = x
  } else if (hNorm * 6 >= 3 && hNorm * 6 < 4) {
    r = 0
    g = x
    b = c
  } else if (hNorm * 6 >= 4 && hNorm * 6 < 5) {
    r = x
    g = 0
    b = c
  } else {
    r = c
    g = 0
    b = x
  }

  r = r + m
  g = g + m
  b = b + m

  // 更精确的RGB到OKLCH转换
  // 使用线性RGB转换
  const linearR = r <= 0.04045 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4)
  const linearG = g <= 0.04045 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4)
  const linearB = b <= 0.04045 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4)

  // 转换到XYZ色彩空间 (D65白点)
  const x_xyz = 0.4124564 * linearR + 0.3575761 * linearG + 0.1804375 * linearB
  const y_xyz = 0.2126729 * linearR + 0.7151522 * linearG + 0.072175 * linearB
  const z_xyz = 0.0193339 * linearR + 0.119192 * linearG + 0.9503041 * linearB

  // 转换到OKLAB色彩空间
  const l_oklab = Math.cbrt(
    0.8189330101 * x_xyz + 0.3618667424 * y_xyz - 0.1288597137 * z_xyz
  )
  const m_oklab = Math.cbrt(
    0.0329845436 * x_xyz + 0.9293118715 * y_xyz + 0.0361456387 * z_xyz
  )
  const s_oklab = Math.cbrt(
    0.0482003018 * x_xyz + 0.2643662691 * y_xyz + 0.633851707 * z_xyz
  )

  // OKLAB到OKLCH
  const lightness =
    0.2104542553 * l_oklab + 0.793617785 * m_oklab - 0.0040720468 * s_oklab
  const a =
    1.9779984951 * l_oklab - 2.428592205 * m_oklab + 0.4505937099 * s_oklab
  const b_lab =
    0.0259040371 * l_oklab + 0.7827717662 * m_oklab - 0.808675766 * s_oklab

  const chroma = Math.sqrt(a * a + b_lab * b_lab)
  let hue = (Math.atan2(b_lab, a) * 180) / Math.PI
  if (hue < 0) hue += 360

  return [lightness, chroma, hue]
}

/**
 * 将OKLCH转换为HSL的简化版本
 */
export function oklchToHsl(
  l: number,
  c: number,
  h: number
): [number, number, number] {
  const hue = h
  const saturation = Math.min(100, c * 100)
  const lightness = l * 100

  return [hue, saturation, lightness]
}

/**
 * 将HSL转换为Hex颜色
 */
export function hslToHex(h: number, s: number, l: number): string {
  const sNorm = s / 100
  const lNorm = l / 100

  const c = (1 - Math.abs(2 * lNorm - 1)) * sNorm
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1))
  const m = lNorm - c / 2

  let r = 0,
    g = 0,
    b = 0

  if (0 <= h && h < 60) {
    r = c
    g = x
    b = 0
  } else if (60 <= h && h < 120) {
    r = x
    g = c
    b = 0
  } else if (120 <= h && h < 180) {
    r = 0
    g = c
    b = x
  } else if (180 <= h && h < 240) {
    r = 0
    g = x
    b = c
  } else if (240 <= h && h < 300) {
    r = x
    g = 0
    b = c
  } else if (300 <= h && h < 360) {
    r = c
    g = 0
    b = x
  }

  r = Math.round((r + m) * 255)
  g = Math.round((g + m) * 255)
  b = Math.round((b + m) * 255)

  return `#${r.toString(16).padStart(2, '0')}${g
    .toString(16)
    .padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

/**
 * 将Hex颜色转换为HSL
 */
export function hexToHsl(hex: string): [number, number, number] {
  const cleanHex = hex.replace('#', '')
  const r = parseInt(cleanHex.substr(0, 2), 16) / 255
  const g = parseInt(cleanHex.substr(2, 2), 16) / 255
  const b = parseInt(cleanHex.substr(4, 2), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min

  let h = 0
  if (diff !== 0) {
    if (max === r) h = ((g - b) / diff) % 6
    else if (max === g) h = (b - r) / diff + 2
    else h = (r - g) / diff + 4
  }
  h = Math.round(h * 60)
  if (h < 0) h += 360

  const l = (max + min) / 2
  const s = diff === 0 ? 0 : diff / (1 - Math.abs(2 * l - 1))

  return [h, Math.round(s * 100), Math.round(l * 100)]
}

/**
 * 基于HSL值生成完整的主题CSS变量
 * 改进版本：更精致的颜色计算，类似第三方网站的高质量主题
 */
export function generateThemeVariables(
  baseHue: number,
  saturation: number,
  lightness: number,
  currentTheme: string = 'light',
  radius: number = 0.5
): Record<string, string> {
  // 根据全局主题模式判断，而不是颜色的lightness值
  const isDark = currentTheme === 'dark'

  // 主色调 - 保持高质量和稳定性，不受滑块过度影响
  const primarySaturation = Math.max(60, Math.min(95, saturation * 0.8 + 20)) // 确保主色调始终有足够饱和度
  const primaryLightness = isDark ? 68 : 42 // 固定的高对比度亮度
  const [primaryL, primaryC, primaryH] = hslToOklch(
    baseHue,
    primarySaturation,
    primaryLightness
  )

  // 亮度滑块主要影响背景色系的明暗程度
  const themeIntensity = lightness / 100
  const bgLightness = isDark
    ? 0.06 + themeIntensity * 0.06 // 深色：0.06-0.12
    : Math.max(0.95, 0.96 + themeIntensity * 0.04) // 浅色：0.96-1.00，确保足够亮

  const fgLightness = isDark ? 0.98 : 0.02 // 前景色保持高对比度

  // 饱和度滑块主要影响背景色系的色彩强度
  const colorIntensity = saturation / 100
  const bgChroma = Math.max(0.005, colorIntensity * 0.025) // 背景色的微妙色彩
  const accentChroma = Math.max(0.01, colorIntensity * 0.08) // 次要元素的色彩强度

  // 次要背景色
  const mutedBgLightness = isDark ? bgLightness + 0.04 : bgLightness - 0.02
  const mutedFgLightness = isDark ? 0.65 : 0.45

  // 边框色 - 确保在深色模式下有足够的对比度
  const borderLightness = isDark
    ? Math.max(0.3, bgLightness + 0.2) // 深色模式下确保边框更明显
    : Math.max(0.85, bgLightness - 0.08) // 浅色模式下确保边框足够暗

  return {
    // 主色调 - 保持高饱和度和对比度，相对稳定
    '--primary': `oklch(${primaryL.toFixed(3)} ${primaryC.toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--primary-foreground': `oklch(${isDark ? '0.02' : '0.98'} 0 0)`,

    // 次要色调 - 受饱和度滑块影响，但保持微妙
    '--secondary': `oklch(${mutedBgLightness.toFixed(3)} ${(
      bgChroma * 1.5
    ).toFixed(3)} ${primaryH.toFixed(1)})`,
    '--secondary-foreground': `oklch(${fgLightness.toFixed(3)} 0 0)`,

    // 强调色 - 稍微调整色相，受饱和度滑块影响
    '--accent': `oklch(${mutedBgLightness.toFixed(3)} ${accentChroma.toFixed(
      3
    )} ${((primaryH + 15) % 360).toFixed(1)})`,
    '--accent-foreground': `oklch(${fgLightness.toFixed(3)} 0 0)`,

    // 静音色调 - 极低饱和度，主要受亮度滑块影响
    '--muted': `oklch(${mutedBgLightness.toFixed(3)} ${(bgChroma * 0.8).toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--muted-foreground': `oklch(${mutedFgLightness.toFixed(3)} 0 0)`,

    // 背景色 - 主要受亮度滑块影响，微妙的色彩倾向
    '--background': `oklch(${bgLightness.toFixed(3)} ${bgChroma.toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--foreground': `oklch(${fgLightness.toFixed(3)} 0 0)`,

    // 卡片背景 - 比主背景稍微突出
    '--card': `oklch(${(isDark
      ? bgLightness + 0.01
      : bgLightness - 0.005
    ).toFixed(3)} ${(bgChroma * 0.8).toFixed(3)} ${primaryH.toFixed(1)})`,
    '--card-foreground': `oklch(${fgLightness.toFixed(3)} 0 0)`,

    // 弹出层背景
    '--popover': `oklch(${bgLightness.toFixed(3)} ${bgChroma.toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--popover-foreground': `oklch(${fgLightness.toFixed(3)} 0 0)`,

    // 边框和输入 - 受亮度滑块影响，微妙的色彩
    '--border': `oklch(${borderLightness.toFixed(3)} ${(bgChroma * 1.2).toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--input': `oklch(${borderLightness.toFixed(3)} ${(bgChroma * 1.2).toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--ring': `oklch(${primaryL.toFixed(3)} ${(primaryC * 0.9).toFixed(
      3
    )} ${primaryH.toFixed(1)})`,

    // 破坏性颜色 - 固定的红色，不受主色调影响
    '--destructive': `oklch(${isDark ? '0.62' : '0.50'} 0.20 29)`,
    '--destructive-foreground': `oklch(${isDark ? '0.02' : '0.98'} 0 0)`,

    // 图表颜色 - 基于主色调生成和谐的变化，保持视觉一致性
    '--chart-1': `oklch(${primaryL.toFixed(3)} ${primaryC.toFixed(
      3
    )} ${primaryH.toFixed(1)})`,
    '--chart-2': `oklch(${primaryL.toFixed(3)} ${(primaryC * 0.95).toFixed(
      3
    )} ${((primaryH + 60) % 360).toFixed(1)})`,
    '--chart-3': `oklch(${(isDark ? primaryL - 0.05 : primaryL + 0.05).toFixed(
      3
    )} ${(primaryC * 0.9).toFixed(3)} ${((primaryH + 120) % 360).toFixed(1)})`,
    '--chart-4': `oklch(${(isDark ? primaryL + 0.05 : primaryL - 0.05).toFixed(
      3
    )} ${(primaryC * 0.85).toFixed(3)} ${((primaryH + 180) % 360).toFixed(1)})`,
    '--chart-5': `oklch(${primaryL.toFixed(3)} ${(primaryC * 0.8).toFixed(
      3
    )} ${((primaryH + 240) % 360).toFixed(1)})`,

    // 圆角半径
    '--radius': `${radius}rem`
  }
}

/**
 * 应用主题变量到DOM
 */
export function applyThemeVariables(
  variables: Record<string, string>,
  currentTheme: string = 'light'
): void {
  // 创建或更新样式标签
  let styleElement = document.getElementById(
    'custom-theme-styles'
  ) as HTMLStyleElement
  if (!styleElement) {
    styleElement = document.createElement('style')
    styleElement.id = 'custom-theme-styles'
    document.head.appendChild(styleElement)
  }

  // 直接使用传入的变量，不再做额外调整
  // 因为generateThemeVariables已经根据当前主题生成了正确的颜色
  const cssVariables = Object.entries(variables)
    .map(([key, value]) => `  ${key}: ${value};`)
    .join('\n')

  // 生成完整的CSS规则，包括对next-themes的支持
  styleElement.textContent = `
    :root.custom-theme {
${cssVariables}
    }

    /* 确保自定义主题优先级高于默认主题 */
    .custom-theme {
      color-scheme: ${currentTheme === 'dark' ? 'dark' : 'light'};
    }

    /* 强制覆盖默认radius设置，确保0rem时真的是0 */
    .custom-theme * {
      --radius-sm: calc(var(--radius) - 4px);
      --radius-md: calc(var(--radius) - 2px);
      --radius-lg: var(--radius);
      --radius-xl: calc(var(--radius) + 4px);
    }
  `

  // 添加自定义主题类到根元素
  const root = document.documentElement
  root.classList.add('custom-theme')
}

/**
 * 移除自定义主题
 */
export function removeCustomTheme(): void {
  const root = document.documentElement
  root.classList.remove('custom-theme')

  const styleElement = document.getElementById('custom-theme-styles')
  if (styleElement) {
    styleElement.remove()
  }
}

/**
 * 预设主题配置
 */
export const PRESET_THEMES = [
  { name: '蓝色', hue: 220, saturation: 70, lightness: 50, color: '#2563eb' },
  { name: '绿色', hue: 142, saturation: 70, lightness: 45, color: '#16a34a' },
  { name: '紫色', hue: 262, saturation: 70, lightness: 50, color: '#9333ea' },
  { name: '红色', hue: 0, saturation: 70, lightness: 50, color: '#dc2626' },
  { name: '橙色', hue: 25, saturation: 80, lightness: 50, color: '#ea580c' },
  { name: '青色', hue: 180, saturation: 70, lightness: 45, color: '#0891b2' },
  { name: '粉色', hue: 330, saturation: 70, lightness: 60, color: '#ec4899' },
  { name: '黄色', hue: 45, saturation: 80, lightness: 55, color: '#eab308' }
] as const

/**
 * 从localStorage加载主题配置
 */
export function loadThemeConfig(): ThemeConfig | null {
  if (typeof window === 'undefined') return null

  try {
    const stored = localStorage.getItem('custom-theme-config')
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

/**
 * 保存主题配置到localStorage
 */
export function saveThemeConfig(config: ThemeConfig): void {
  if (typeof window === 'undefined') return

  localStorage.setItem('custom-theme-config', JSON.stringify(config))
}

/**
 * 检查是否启用了自定义主题
 */
export function isCustomThemeActive(): boolean {
  if (typeof window === 'undefined') return false

  return localStorage.getItem('custom-theme-active') === 'true'
}

/**
 * 设置自定义主题状态
 */
export function setCustomThemeActive(active: boolean): void {
  if (typeof window === 'undefined') return

  localStorage.setItem('custom-theme-active', active.toString())
}

/**
 * 快速应用预设主题
 */
export function applyPresetTheme(
  preset: (typeof PRESET_THEMES)[number],
  currentTheme: string = 'light'
): void {
  const variables = generateThemeVariables(
    preset.hue,
    preset.saturation,
    preset.lightness,
    currentTheme,
    0.5 // 默认圆角
  )
  applyThemeVariables(variables, currentTheme)

  // 保存配置
  saveThemeConfig({
    hue: preset.hue,
    saturation: preset.saturation,
    lightness: preset.lightness
  })
  setCustomThemeActive(true)
}

/**
 * 从Hex颜色快速创建主题
 */
export function createThemeFromHex(
  hexColor: string,
  currentTheme: string = 'light'
): void {
  const [h, s, l] = hexToHsl(hexColor)
  const variables = generateThemeVariables(h, s, l, currentTheme, 0.5)
  applyThemeVariables(variables, currentTheme)

  // 保存配置
  saveThemeConfig({ hue: h, saturation: s, lightness: l })
  setCustomThemeActive(true)
}

/**
 * 生成HSL格式的主题CSS变量（用于复制）
 * 改进版本：更精致的颜色计算，类似shadcn/ui官方主题
 */
export function generateHslThemeCSS(
  baseHue: number,
  saturation: number,
  lightness: number,
  currentTheme: string = 'light',
  radius: number = 0.5
): string {
  // 将HSL值转换为CSS HSL格式（带hsl()包装）
  const formatHsl = (h: number, s: number, l: number) => `hsl(${h} ${s}% ${l}%)`

  // 根据全局主题模式判断，而不是颜色的lightness值
  const isDark = currentTheme === 'dark'

  // 主色调 - 保持高质量和稳定性，与OKLCH版本一致
  const primarySaturation = Math.max(
    60,
    Math.min(95, Math.round(saturation * 0.8 + 20))
  )
  const primaryLightness = isDark ? 68 : 42 // 固定的高对比度亮度

  // 亮度滑块主要影响背景色系的明暗程度
  const themeIntensity = lightness / 100
  const bgLightness = isDark
    ? Math.round(6 + themeIntensity * 6) // 深色：6-12%
    : Math.max(95, Math.round(96 + themeIntensity * 4)) // 浅色：96-100%，确保足够亮

  const fgLightness = isDark ? 98 : 2 // 前景色保持高对比度

  // 饱和度滑块主要影响背景色系的色彩强度
  const colorIntensity = saturation / 100
  const bgSaturation = Math.max(1, Math.round(colorIntensity * 8)) // 背景色的微妙色彩
  const accentSaturation = Math.max(3, Math.round(colorIntensity * 20)) // 次要元素的色彩强度

  // 次要背景色
  const mutedBgLightness = isDark
    ? Math.round(bgLightness + 4)
    : Math.round(bgLightness - 2)
  const mutedBgSaturation = Math.max(1, Math.round(bgSaturation * 1.5))

  // 静音前景色
  const mutedFgLightness = isDark ? 65 : 45

  // 边框色 - 确保在深色模式下有足够的对比度
  const borderLightness = isDark
    ? Math.max(30, Math.round(bgLightness + 20)) // 深色模式下确保边框更明显
    : Math.max(85, Math.round(bgLightness - 8)) // 浅色模式下确保边框足够暗
  const borderSaturation = Math.max(1, Math.round(bgSaturation * 1.2))

  // 计算浅色主题的颜色值
  const lightBgLightness = Math.max(95, Math.round(96 + (lightness / 100) * 4))
  const lightFgLightness = 2
  const lightMutedBgLightness = Math.round(lightBgLightness - 2)
  const lightBorderLightness = Math.max(85, Math.round(lightBgLightness - 8))

  // 计算深色主题的颜色值
  const darkBgLightness = Math.round(6 + (lightness / 100) * 6)
  const darkFgLightness = 98
  const darkMutedBgLightness = Math.round(darkBgLightness + 4)
  const darkBorderLightness = Math.max(30, Math.round(darkBgLightness + 20))

  // 生成完整的主题CSS（包含浅色和深色主题）
  const themeCSS = `
:root {
  --background: ${formatHsl(baseHue, bgSaturation, lightBgLightness)};
  --foreground: ${formatHsl(0, 0, lightFgLightness)};
  --card: ${formatHsl(
    baseHue,
    Math.round(bgSaturation * 0.8),
    lightBgLightness - 1
  )};
  --card-foreground: ${formatHsl(0, 0, lightFgLightness)};
  --popover: ${formatHsl(baseHue, bgSaturation, lightBgLightness)};
  --popover-foreground: ${formatHsl(0, 0, lightFgLightness)};
  --primary: ${formatHsl(baseHue, primarySaturation, 42)};
  --primary-foreground: ${formatHsl(0, 0, 98)};
  --secondary: ${formatHsl(baseHue, mutedBgSaturation, lightMutedBgLightness)};
  --secondary-foreground: ${formatHsl(0, 0, lightFgLightness)};
  --muted: ${formatHsl(
    baseHue,
    Math.round(mutedBgSaturation * 0.8),
    lightMutedBgLightness
  )};
  --muted-foreground: ${formatHsl(0, 0, 45)};
  --accent: ${formatHsl(
    (baseHue + 15) % 360,
    accentSaturation,
    lightMutedBgLightness
  )};
  --accent-foreground: ${formatHsl(0, 0, lightFgLightness)};
  --destructive: ${formatHsl(0, 84, 50)};
  --destructive-foreground: ${formatHsl(0, 0, 98)};
  --border: ${formatHsl(baseHue, borderSaturation, lightBorderLightness)};
  --input: ${formatHsl(baseHue, borderSaturation, lightBorderLightness)};
  --ring: ${formatHsl(baseHue, Math.round(primarySaturation * 0.9), 42)};
  --radius: ${radius}rem;
}

.dark {
  --background: ${formatHsl(baseHue, bgSaturation, darkBgLightness)};
  --foreground: ${formatHsl(0, 0, darkFgLightness)};
  --card: ${formatHsl(
    baseHue,
    Math.round(bgSaturation * 0.8),
    darkBgLightness + 1
  )};
  --card-foreground: ${formatHsl(0, 0, darkFgLightness)};
  --popover: ${formatHsl(baseHue, bgSaturation, darkBgLightness)};
  --popover-foreground: ${formatHsl(0, 0, darkFgLightness)};
  --primary: ${formatHsl(baseHue, primarySaturation, 68)};
  --primary-foreground: ${formatHsl(0, 0, 2)};
  --secondary: ${formatHsl(baseHue, mutedBgSaturation, darkMutedBgLightness)};
  --secondary-foreground: ${formatHsl(0, 0, darkFgLightness)};
  --muted: ${formatHsl(
    baseHue,
    Math.round(mutedBgSaturation * 0.8),
    darkMutedBgLightness
  )};
  --muted-foreground: ${formatHsl(0, 0, 65)};
  --accent: ${formatHsl(
    (baseHue + 15) % 360,
    accentSaturation,
    darkMutedBgLightness
  )};
  --accent-foreground: ${formatHsl(0, 0, darkFgLightness)};
  --destructive: ${formatHsl(0, 62, 55)};
  --destructive-foreground: ${formatHsl(0, 0, 2)};
  --border: ${formatHsl(baseHue, borderSaturation, darkBorderLightness)};
  --input: ${formatHsl(baseHue, borderSaturation, darkBorderLightness)};
  --ring: ${formatHsl(baseHue, Math.round(primarySaturation * 0.9), 68)};
}`

  return themeCSS.trim()
}

/**
 * 生成OKLCH格式的主题CSS变量（用于复制）
 * 改进版本：与HSL版本保持一致的逻辑
 */
export function generateOklchThemeCSS(
  baseHue: number,
  saturation: number,
  lightness: number,
  currentTheme: string = 'light',
  radius: number = 0.5
): string {
  // 格式化OKLCH值（带oklch()包装）
  const formatOklch = (l: number, c: number, h: number) => {
    return `oklch(${l.toFixed(3)} ${c.toFixed(3)} ${h.toFixed(1)})`
  }

  // 根据全局主题模式判断，而不是颜色的lightness值
  const isDark = currentTheme === 'dark'

  // 主色调 - 与HSL版本保持一致的逻辑
  const primarySaturation = Math.max(
    60,
    Math.min(95, Math.round(saturation * 0.8 + 20))
  )
  const primaryLightness = isDark ? 68 : 42
  const [primaryL, primaryC, primaryH] = hslToOklch(
    baseHue,
    primarySaturation,
    primaryLightness
  )

  // 亮度滑块主要影响背景色系的明暗程度
  const themeIntensity = lightness / 100
  const bgLightness = isDark
    ? 0.06 + themeIntensity * 0.06 // 深色：0.06-0.12
    : Math.max(0.95, 0.96 + themeIntensity * 0.04) // 浅色：0.96-1.00，确保足够亮

  const fgLightness = isDark ? 0.98 : 0.02

  // 饱和度滑块主要影响背景色系的色彩强度
  const colorIntensity = saturation / 100
  const bgChroma = Math.max(0.005, colorIntensity * 0.025) // 背景色的微妙色彩
  const accentChroma = Math.max(0.01, colorIntensity * 0.08) // 次要元素的色彩强度

  // 次要背景色
  const mutedBgLightness = isDark ? bgLightness + 0.04 : bgLightness - 0.02
  const mutedBgChroma = bgChroma * 1.5

  // 静音前景色
  const mutedFgLightness = isDark ? 0.65 : 0.45

  // 边框色 - 确保在深色模式下有足够的对比度
  const borderLightness = isDark
    ? Math.max(0.3, bgLightness + 0.2) // 深色模式下确保边框更明显
    : Math.max(0.85, bgLightness - 0.08) // 浅色模式下确保边框足够暗
  const borderChroma = bgChroma * 1.2

  // 计算浅色主题的颜色值
  const lightBgLightness = Math.max(0.95, 0.96 + (lightness / 100) * 0.04)
  const lightFgLightness = 0.02
  const lightMutedBgLightness = lightBgLightness - 0.02
  const lightBorderLightness = Math.max(0.85, lightBgLightness - 0.08)
  const [lightPrimaryL, lightPrimaryC, lightPrimaryH] = hslToOklch(
    baseHue,
    primarySaturation,
    42
  )

  // 计算深色主题的颜色值
  const darkBgLightness = 0.06 + (lightness / 100) * 0.06
  const darkFgLightness = 0.98
  const darkMutedBgLightness = darkBgLightness + 0.04
  const darkBorderLightness = Math.max(0.3, darkBgLightness + 0.2)
  const [darkPrimaryL, darkPrimaryC, darkPrimaryH] = hslToOklch(
    baseHue,
    primarySaturation,
    68
  )

  // 生成完整的主题CSS（包含浅色和深色主题）
  const themeCSS = `
:root {
  --background: ${formatOklch(lightBgLightness, bgChroma, baseHue)};
  --foreground: ${formatOklch(lightFgLightness, 0, 0)};
  --card: ${formatOklch(lightBgLightness - 0.005, bgChroma * 0.8, baseHue)};
  --card-foreground: ${formatOklch(lightFgLightness, 0, 0)};
  --popover: ${formatOklch(lightBgLightness, bgChroma, baseHue)};
  --popover-foreground: ${formatOklch(lightFgLightness, 0, 0)};
  --primary: ${formatOklch(lightPrimaryL, lightPrimaryC, lightPrimaryH)};
  --primary-foreground: ${formatOklch(0.98, 0, 0)};
  --secondary: ${formatOklch(lightMutedBgLightness, mutedBgChroma, baseHue)};
  --secondary-foreground: ${formatOklch(lightFgLightness, 0, 0)};
  --muted: ${formatOklch(lightMutedBgLightness, mutedBgChroma * 0.8, baseHue)};
  --muted-foreground: ${formatOklch(0.45, 0, 0)};
  --accent: ${formatOklch(
    lightMutedBgLightness,
    accentChroma,
    (baseHue + 15) % 360
  )};
  --accent-foreground: ${formatOklch(lightFgLightness, 0, 0)};
  --destructive: ${formatOklch(0.5, 0.2, 29)};
  --destructive-foreground: ${formatOklch(0.98, 0, 0)};
  --border: ${formatOklch(lightBorderLightness, borderChroma, baseHue)};
  --input: ${formatOklch(lightBorderLightness, borderChroma, baseHue)};
  --ring: ${formatOklch(lightPrimaryL, lightPrimaryC * 0.9, lightPrimaryH)};
  --radius: ${radius}rem;
}

.dark {
  --background: ${formatOklch(darkBgLightness, bgChroma, baseHue)};
  --foreground: ${formatOklch(darkFgLightness, 0, 0)};
  --card: ${formatOklch(darkBgLightness + 0.01, bgChroma * 0.8, baseHue)};
  --card-foreground: ${formatOklch(darkFgLightness, 0, 0)};
  --popover: ${formatOklch(darkBgLightness, bgChroma, baseHue)};
  --popover-foreground: ${formatOklch(darkFgLightness, 0, 0)};
  --primary: ${formatOklch(darkPrimaryL, darkPrimaryC, darkPrimaryH)};
  --primary-foreground: ${formatOklch(0.02, 0, 0)};
  --secondary: ${formatOklch(darkMutedBgLightness, mutedBgChroma, baseHue)};
  --secondary-foreground: ${formatOklch(darkFgLightness, 0, 0)};
  --muted: ${formatOklch(darkMutedBgLightness, mutedBgChroma * 0.8, baseHue)};
  --muted-foreground: ${formatOklch(0.65, 0, 0)};
  --accent: ${formatOklch(
    darkMutedBgLightness,
    accentChroma,
    (baseHue + 15) % 360
  )};
  --accent-foreground: ${formatOklch(darkFgLightness, 0, 0)};
  --destructive: ${formatOklch(0.62, 0.2, 29)};
  --destructive-foreground: ${formatOklch(0.02, 0, 0)};
  --border: ${formatOklch(darkBorderLightness, borderChroma, baseHue)};
  --input: ${formatOklch(darkBorderLightness, borderChroma, baseHue)};
  --ring: ${formatOklch(darkPrimaryL, darkPrimaryC * 0.9, darkPrimaryH)};
}`

  return themeCSS.trim()
}

/**
 * CSS格式类型
 */
export type CSSFormat = 'hsl' | 'oklch'

/**
 * 根据格式生成主题CSS
 */
export function generateThemeCSS(
  baseHue: number,
  saturation: number,
  lightness: number,
  format: CSSFormat = 'hsl',
  currentTheme: string = 'light',
  radius: number = 0.5
): string {
  switch (format) {
    case 'oklch':
      return generateOklchThemeCSS(
        baseHue,
        saturation,
        lightness,
        currentTheme,
        radius
      )
    case 'hsl':
    default:
      return generateHslThemeCSS(
        baseHue,
        saturation,
        lightness,
        currentTheme,
        radius
      )
  }
}
