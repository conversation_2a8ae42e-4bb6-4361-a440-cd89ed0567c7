'use client'

import {
  Calendar,
  CalendarDays,
  CreditCard,
  Settings,
  User,
  Mail,
  Bell,
  Search,
  MoreHorizontal,
  Plus,
  Star,
  Heart,
  MessageSquare,
  Share,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

/**
 * 样式预览组件
 * 参考 shadcn/ui 主题页面的组件展示风格
 */
export function StylePreview() {
  return (
    <div className="space-y-8">
      {/* Dashboard Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$15,231.89</div>
            <p className="text-xs text-muted-foreground">
              +20.1% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subscriptions</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+2,350</div>
            <p className="text-xs text-muted-foreground">
              +180.1% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sales</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12,234</div>
            <p className="text-xs text-muted-foreground">
              +19% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Now</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+573</div>
            <p className="text-xs text-muted-foreground">
              +201 since last hour
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Chart Card */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Overview</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[200px] flex items-end justify-between gap-2">
              {[40, 80, 60, 90, 70, 85, 65, 75, 95, 55, 80, 70].map(
                (height, i) => (
                  <div
                    key={i}
                    className="bg-primary rounded-sm flex-1"
                    style={{ height: `${height}%` }}
                  />
                )
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Sales */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Sales</CardTitle>
            <CardDescription>You made 265 sales this month.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {[
                {
                  name: 'Olivia Martin',
                  email: '<EMAIL>',
                  amount: '+$1,999.00'
                },
                {
                  name: 'Jackson Lee',
                  email: '<EMAIL>',
                  amount: '+$39.00'
                },
                {
                  name: 'Isabella Nguyen',
                  email: '<EMAIL>',
                  amount: '+$299.00'
                },
                {
                  name: 'William Kim',
                  email: '<EMAIL>',
                  amount: '+$99.00'
                },
                {
                  name: 'Sofia Davis',
                  email: '<EMAIL>',
                  amount: '+$39.00'
                }
              ].map((sale, i) => (
                <div
                  key={i}
                  className="flex items-center">
                  <div className="w-9 h-9 rounded-full bg-muted flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {sale.name
                        .split(' ')
                        .map(n => n[0])
                        .join('')}
                    </span>
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {sale.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {sale.email}
                    </p>
                  </div>
                  <div className="ml-auto font-medium">{sale.amount}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Components Showcase */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Buttons & Form Elements */}
        <Card>
          <CardHeader>
            <CardTitle>Components</CardTitle>
            <CardDescription>
              A collection of reusable components.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Buttons */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Buttons</Label>
              <div className="flex flex-wrap gap-2">
                <Button>Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="destructive">Destructive</Button>
              </div>
            </div>

            {/* Form */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Form Elements</Label>
              <div className="space-y-3">
                <Input placeholder="Email address" />
                <div className="flex items-center space-x-2">
                  <Switch id="notifications" />
                  <Label htmlFor="notifications">Email notifications</Label>
                </div>
              </div>
            </div>

            {/* Badges */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Badges</Label>
              <div className="flex flex-wrap gap-2">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings Card */}
        <Card>
          <CardHeader>
            <CardTitle>Settings</CardTitle>
            <CardDescription>
              Manage your account settings and preferences.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Tabs
              defaultValue="account"
              className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="account">Account</TabsTrigger>
                <TabsTrigger value="password">Password</TabsTrigger>
              </TabsList>
              <TabsContent
                value="account"
                className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    defaultValue="Pedro Duarte"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    defaultValue="@peduarte"
                  />
                </div>
              </TabsContent>
              <TabsContent
                value="password"
                className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current">Current password</Label>
                  <Input
                    id="current"
                    type="password"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new">New password</Label>
                  <Input
                    id="new"
                    type="password"
                  />
                </div>
              </TabsContent>
            </Tabs>
            <Button className="w-full">Save changes</Button>
          </CardContent>
        </Card>
      </div>

      {/* Progress & Status */}
      <Card>
        <CardHeader>
          <CardTitle>Progress & Status</CardTitle>
          <CardDescription>
            Visual indicators and progress bars.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Storage Usage</Label>
              <span className="text-sm text-muted-foreground">68%</span>
            </div>
            <Progress
              value={68}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Memory Usage</Label>
              <span className="text-sm text-muted-foreground">45%</span>
            </div>
            <Progress
              value={45}
              className="w-full"
            />
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-20 bg-primary/10 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">24</div>
                  <div className="text-xs text-muted-foreground">
                    Active Users
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-20 bg-secondary/10 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold text-secondary-foreground">
                    12
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Pending Tasks
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
