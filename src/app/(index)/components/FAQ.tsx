'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Mail
} from 'lucide-react'

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([0]) // First item open by default

  const faqs = [
    {
      question: '如何开始使用这个平台？',
      answer:
        '注册账户后，您可以立即开始使用我们的平台。我们提供详细的入门指南和教程，帮助您快速上手。个人版用户可以免费使用基础功能，专业版和企业版用户享有更多高级功能。'
    },
    {
      question: '是否提供免费试用？',
      answer:
        '是的！我们为所有付费方案提供 14 天免费试用。在试用期间，您可以体验所有功能，无需提供信用卡信息。试用结束后，您可以选择继续使用付费方案或降级到免费的个人版。'
    },
    {
      question: '如何升级或降级我的方案？',
      answer:
        '您可以随时在账户设置中升级或降级您的方案。升级立即生效，降级将在当前计费周期结束后生效。我们会按比例退还未使用的费用，确保您只为实际使用的服务付费。'
    },
    {
      question: '数据安全性如何保障？',
      answer:
        '我们采用企业级安全措施保护您的数据，包括 SSL 加密传输、数据库加密存储、定期安全审计和备份。我们符合 GDPR、SOC 2 等国际安全标准，确保您的数据安全无忧。'
    },
    {
      question: '支持哪些集成和 API？',
      answer:
        '我们提供丰富的集成选项，支持主流的开发工具、CI/CD 平台、云服务提供商等。同时提供完整的 REST API 和 GraphQL API，方便您进行自定义集成和开发。'
    },
    {
      question: '如何获得技术支持？',
      answer:
        '我们提供多种支持渠道：在线文档、社区论坛、邮件支持和实时聊天。专业版用户享有优先支持，企业版用户配备专属客户经理，提供 24/7 技术支持。'
    },
    {
      question: '可以取消订阅吗？',
      answer:
        '当然可以。您可以随时取消订阅，取消后您的账户将在当前计费周期结束后自动降级到免费的个人版。我们不收取任何取消费用，您的数据将继续保留。'
    },
    {
      question: '是否支持团队协作？',
      answer:
        '是的！专业版和企业版都支持团队协作功能，包括成员管理、权限控制、实时协作编辑、评论和审批流程等。您可以邀请团队成员加入项目，共同协作完成工作。'
    }
  ]

  const toggleItem = (index: number) => {
    setOpenItems(prev =>
      prev.includes(index) ? prev.filter(i => i !== index) : [...prev, index]
    )
  }

  return (
    <section
      id="faq"
      className="py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            常见
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              问题解答
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            找到您关心问题的答案。如果您有其他疑问，请随时联系我们的支持团队。
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ items */}
          <div className="space-y-4 mb-16">
            {faqs.map((faq, index) => {
              const isOpen = openItems.includes(index)

              return (
                <Card
                  key={index}
                  className="overflow-hidden">
                  <button
                    onClick={() => toggleItem(index)}
                    className="w-full text-left p-6 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold pr-4">
                        {faq.question}
                      </h3>
                      {isOpen ? (
                        <ChevronUp className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                      )}
                    </div>
                  </button>

                  {isOpen && (
                    <CardContent className="pt-0 pb-6">
                      <p className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </p>
                    </CardContent>
                  )}
                </Card>
              )
            })}
          </div>

          {/* Contact section */}
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
              <HelpCircle className="w-4 h-4" />
              <span>还有其他问题？</span>
            </div>

            <h3 className="text-2xl font-bold mb-4">我们随时为您提供帮助</h3>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              如果您没有找到想要的答案，我们的支持团队随时准备为您提供帮助。
              选择最适合您的联系方式。
            </p>

            <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <Card className="p-6 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">在线聊天</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  与我们的支持团队实时对话
                </p>
                <Button
                  variant="outline"
                  className="w-full">
                  开始聊天
                </Button>
              </Card>

              <Card className="p-6 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Mail className="w-6 h-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">邮件支持</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  发送邮件，我们会尽快回复
                </p>
                <Button
                  variant="outline"
                  className="w-full">
                  发送邮件
                </Button>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
