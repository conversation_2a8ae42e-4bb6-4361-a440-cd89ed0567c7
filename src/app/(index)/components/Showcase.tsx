'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Play,
  Monitor,
  Smartphone,
  Tablet,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

export default function Showcase() {
  const [activeTab, setActiveTab] = useState(0)
  const [currentSlide, setCurrentSlide] = useState(0)

  const showcaseItems = [
    {
      title: '仪表板界面',
      description: '直观的数据可视化，让复杂信息一目了然',
      category: '数据分析',
      image: '/api/placeholder/800/600',
      features: ['实时数据更新', '自定义图表', '多维度分析']
    },
    {
      title: '移动应用',
      description: '原生移动体验，随时随地管理您的业务',
      category: '移动端',
      image: '/api/placeholder/400/800',
      features: ['离线同步', '推送通知', '生物识别']
    },
    {
      title: '协作工具',
      description: '团队协作从未如此简单高效',
      category: '团队协作',
      image: '/api/placeholder/800/600',
      features: ['实时协作', '版本控制', '权限管理']
    }
  ]

  const devices = [
    { name: '桌面端', icon: Monitor },
    { name: '平板端', icon: Tablet },
    { name: '移动端', icon: Smartphone }
  ]

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % showcaseItems.length)
  }

  const prevSlide = () => {
    setCurrentSlide(
      prev => (prev - 1 + showcaseItems.length) % showcaseItems.length
    )
  }

  return (
    <section
      id="showcase"
      className="py-16 md:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            产品
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              实际展示
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            看看我们的产品在实际使用中的表现，体验流畅的用户界面和强大的功能。
          </p>
        </div>

        {/* Device tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-background rounded-lg p-1 border">
            {devices.map((device, index) => {
              const Icon = device.icon
              return (
                <Button
                  key={index}
                  variant={activeTab === index ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab(index)}
                  className="flex items-center gap-2">
                  <Icon className="w-4 h-4" />
                  {device.name}
                </Button>
              )
            })}
          </div>
        </div>

        {/* Main showcase */}
        <div className="relative">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-0">
                {/* Content side */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <Badge
                    variant="secondary"
                    className="w-fit mb-4">
                    {showcaseItems[currentSlide].category}
                  </Badge>

                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    {showcaseItems[currentSlide].title}
                  </h3>

                  <p className="text-muted-foreground text-lg mb-6">
                    {showcaseItems[currentSlide].description}
                  </p>

                  <ul className="space-y-2 mb-8">
                    {showcaseItems[currentSlide].features.map(
                      (feature, index) => (
                        <li
                          key={index}
                          className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-primary" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      )
                    )}
                  </ul>

                  <div className="flex items-center gap-4">
                    <Button>
                      <Play className="w-4 h-4 mr-2" />
                      观看演示
                    </Button>
                    <Button variant="outline">了解更多</Button>
                  </div>
                </div>

                {/* Image side */}
                <div className="relative bg-gradient-to-br from-primary/5 to-secondary/5 p-8 lg:p-12 flex items-center justify-center">
                  <div className="relative w-full max-w-md">
                    {/* Mock device frame */}
                    <div
                      className={`relative ${
                        activeTab === 0
                          ? 'aspect-video'
                          : activeTab === 1
                          ? 'aspect-[3/4]'
                          : 'aspect-[9/16]'
                      } bg-background rounded-lg border-8 border-muted shadow-2xl overflow-hidden`}>
                      {/* Mock screen content */}
                      <div className="w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary/20 flex items-center justify-center">
                            <Play className="w-8 h-8 text-primary" />
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {showcaseItems[currentSlide].title}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Floating elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/20 rounded-full blur-sm" />
                    <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary/20 rounded-full blur-sm" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation arrows */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10"
            onClick={prevSlide}>
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10"
            onClick={nextSlide}>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Slide indicators */}
        <div className="flex justify-center mt-8 gap-2">
          {showcaseItems.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-primary' : 'bg-muted-foreground/30'
              }`}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
