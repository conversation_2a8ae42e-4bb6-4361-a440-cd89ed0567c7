'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Github,
  Twitter,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  ArrowUp
} from 'lucide-react'

export default function Footer() {
  const footerLinks = {
    product: {
      title: '产品',
      links: [
        { name: '功能特性', href: '#features' },
        { name: '产品展示', href: '#showcase' },
        { name: '定价方案', href: '#pricing' },
        { name: '更新日志', href: '/changelog' },
        { name: '路线图', href: '/roadmap' }
      ]
    },
    developers: {
      title: '开发者',
      links: [
        { name: 'API 文档', href: '/docs/api' },
        { name: '开发指南', href: '/docs/guides' },
        { name: 'SDK 下载', href: '/docs/sdk' },
        { name: '示例代码', href: '/examples' },
        { name: '开发者社区', href: '/community' }
      ]
    },
    company: {
      title: '公司',
      links: [
        { name: '关于我们', href: '/about' },
        { name: '团队介绍', href: '/team' },
        { name: '新闻动态', href: '/news' },
        { name: '招聘信息', href: '/careers' },
        { name: '联系我们', href: '/contact' }
      ]
    },
    support: {
      title: '支持',
      links: [
        { name: '帮助中心', href: '/help' },
        { name: '常见问题', href: '#faq' },
        { name: '技术支持', href: '/support' },
        { name: '服务状态', href: '/status' },
        { name: '反馈建议', href: '/feedback' }
      ]
    }
  }

  const socialLinks = [
    { name: 'GitHub', icon: Github, href: 'https://github.com' },
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com' },
    { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com' },
    { name: 'Email', icon: Mail, href: 'mailto:<EMAIL>' }
  ]

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <footer className="bg-muted/30 border-t">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Company info */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-lg">
                    S
                  </span>
                </div>
                <span className="font-bold text-xl">Starter</span>
              </div>

              <p className="text-muted-foreground mb-6 max-w-sm">
                构建下一代数字化解决方案的强大平台。
                让创新变得简单，让成功触手可及。
              </p>

              {/* Contact info */}
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>北京市朝阳区科技园区</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>+86 ************</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Footer links */}
            {Object.entries(footerLinks).map(([key, section]) => (
              <div key={key}>
                <h3 className="font-semibold mb-4">{section.title}</h3>
                <ul className="space-y-2">
                  {section.links.map(link => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Bottom footer */}
        <div className="py-6 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex flex-col md:flex-row items-center gap-4 text-sm text-muted-foreground">
            <span>© 2024 Starter. 保留所有权利。</span>
            <div className="flex items-center gap-4">
              <Link
                href="/privacy"
                className="hover:text-foreground transition-colors">
                隐私政策
              </Link>
              <Link
                href="/terms"
                className="hover:text-foreground transition-colors">
                服务条款
              </Link>
              <Link
                href="/cookies"
                className="hover:text-foreground transition-colors">
                Cookie 政策
              </Link>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Social links */}
            <div className="flex items-center gap-2">
              {socialLinks.map(social => {
                const Icon = social.icon
                return (
                  <Link
                    key={social.name}
                    href={social.href}
                    className="w-8 h-8 rounded-lg bg-muted hover:bg-muted-foreground/20 flex items-center justify-center transition-colors"
                    target="_blank"
                    rel="noopener noreferrer">
                    <Icon className="w-4 h-4" />
                  </Link>
                )
              })}
            </div>

            {/* Back to top */}
            <Button
              variant="outline"
              size="icon"
              onClick={scrollToTop}
              className="w-8 h-8">
              <ArrowUp className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </footer>
  )
}
