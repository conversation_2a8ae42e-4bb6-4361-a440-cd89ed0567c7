'use client'

import { useEffect } from 'react'
import { useTheme } from 'next-themes'
import {
  loadThemeConfig,
  isCustomThemeActive,
  generateThemeVariables,
  applyThemeVariables,
  removeCustomTheme
} from '@/utils/theme'

/**
 * 全局主题管理器
 * 负责在整个应用中自动应用和同步自定义主题
 */
export function GlobalThemeManager() {
  const { resolvedTheme } = useTheme()

  // 应用自定义主题的函数
  const applyCustomTheme = () => {
    if (isCustomThemeActive()) {
      const themeConfig = loadThemeConfig()

      if (themeConfig) {
        const variables = generateThemeVariables(
          themeConfig.hue,
          themeConfig.saturation,
          themeConfig.lightness,
          resolvedTheme || 'light',
          themeConfig.radius || 0.5
        )

        applyThemeVariables(variables, resolvedTheme || 'light')
      }
    } else {
      // 如果自定义主题未激活，移除自定义样式
      removeCustomTheme()
    }
  }

  // 初始化时应用主题
  useEffect(() => {
    // 延迟应用，确保DOM已准备好
    const timer = setTimeout(() => {
      applyCustomTheme()
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // 当系统主题改变时重新应用自定义主题
  useEffect(() => {
    applyCustomTheme()
  }, [resolvedTheme])

  // 监听localStorage变化，实现跨标签页同步
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'custom-theme-config' || e.key === 'custom-theme-active') {
        // 当主题配置或状态改变时，重新应用主题
        setTimeout(() => {
          applyCustomTheme()
        }, 50)
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [resolvedTheme])

  // 监听系统主题偏好变化
  useEffect(() => {
    const handleThemeChange = () => {
      // 当系统主题偏好改变时，重新应用自定义主题
      setTimeout(() => {
        applyCustomTheme()
      }, 50)
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', handleThemeChange)

    return () => {
      mediaQuery.removeEventListener('change', handleThemeChange)
    }
  }, [resolvedTheme])

  // 监听DOM变化，确保自定义主题类不被意外移除
  useEffect(() => {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (
          mutation.type === 'attributes' &&
          mutation.attributeName === 'class'
        ) {
          const target = mutation.target as HTMLElement
          if (target === document.documentElement && isCustomThemeActive()) {
            // 如果根元素的class被修改且自定义主题是激活的，确保自定义主题类存在
            if (!target.classList.contains('custom-theme')) {
              target.classList.add('custom-theme')
            }
          }
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    return () => {
      observer.disconnect()
    }
  }, [])

  return null
}
