'use client'

import { useState, useEffect } from 'react'
import { Moon, Sun } from 'lucide-react'
import { useTheme } from 'next-themes'
import { HexColorPicker } from 'react-colorful'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { useCustomTheme } from '@/hooks/use-custom-theme'
import { PRESET_THEMES, hexToHsl, hslToHex } from '@/utils/theme'
import { ThemeCSSExportDialog } from './theme-css-export-dialog'

/**
 * 主题控制组件
 * 合并了颜色选择、精确调整和主题控制功能
 */
export function ThemeControls() {
  const { theme, setTheme } = useTheme()
  const {
    themeConfig,
    isCustomThemeActive,
    applyTheme,
    resetTheme,
    updateThemeConfig,
    updateFromHexColor,
    setPresetTheme,
    toggleCustomTheme,
    getCurrentHexColor,
    getCurrentOklchValues
  } = useCustomTheme()

  // 获取用于颜色选择器显示的颜色（固定饱和度和亮度，只反映色相）
  const getColorPickerHex = () => {
    return hslToHex(themeConfig.hue, 70, 50) // 固定70%饱和度，50%亮度
  }

  const [colorInput, setColorInput] = useState(getColorPickerHex())

  // 只在色相变化时同步更新颜色输入框，不受饱和度和亮度滑块影响
  useEffect(() => {
    setColorInput(getColorPickerHex())
  }, [themeConfig.hue]) // 只监听色相变化

  // 从颜色选择器更新主题配置（更新所有HSL值）
  const updateFromColorPicker = (color: string) => {
    setColorInput(color)
    updateFromHexColor(color)
  }

  // 从颜色输入框更新主题配置（只更新色相，保持饱和度和亮度不变）
  const updateFromColorInput = (color: string) => {
    setColorInput(color)
    try {
      const [h] = hexToHsl(color) // 只提取色相
      updateThemeConfig({ hue: h }) // 只更新色相，保持其他值不变
    } catch (error) {
      // 如果颜色格式无效，忽略错误
      console.warn('Invalid color format:', color)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div
            className="w-4 h-4 rounded-full border"
            style={{ backgroundColor: colorInput }}
          />
          主题配置
        </CardTitle>
        <CardDescription>
          选择基础颜色、调整色彩强度和亮度并管理主题设置
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* 颜色选择区域 */}
        <div className="space-y-6">
          <div className="space-y-3">
            <Label htmlFor="color-picker">颜色选择器</Label>
            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <HexColorPicker
                  color={colorInput}
                  onChange={updateFromColorPicker}
                  style={{ width: '200px', height: '200px' }}
                />
              </div>
              <div className="flex-1 space-y-3">
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">
                    颜色代码
                  </Label>
                  <Input
                    type="text"
                    value={colorInput}
                    onChange={e => updateFromColorInput(e.target.value)}
                    placeholder="#3b82f6"
                    className="h-12 font-mono"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">
                    颜色预览
                  </Label>
                  <div
                    className="max-w-32 h-16 rounded-lg border-2 border-border"
                    style={{ backgroundColor: colorInput }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 预设颜色 */}
          <div className="space-y-3">
            <Label>预设颜色</Label>
            <div className="grid grid-cols-4 gap-3">
              {PRESET_THEMES.map(preset => (
                <button
                  key={preset.name}
                  onClick={() => {
                    setPresetTheme(preset)
                  }}
                  className="group flex flex-col items-center gap-2 p-3 rounded-xl border-2 border-transparent hover:border-primary/20 hover:bg-muted/50 transition-all duration-200"
                  title={preset.name}>
                  <div
                    className="w-8 h-8 rounded-full border-2 border-background shadow-sm group-hover:scale-110 transition-transform duration-200"
                    style={{ backgroundColor: preset.color }}
                  />
                  <span className="text-xs font-medium">{preset.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 精确调整区域 */}
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">色彩强度</Label>
                  <p className="text-xs text-muted-foreground">
                    控制整个主题的色彩饱和度
                  </p>
                </div>
                <span className="text-sm text-muted-foreground font-mono">
                  {themeConfig.saturation}%
                </span>
              </div>
              <Slider
                value={[themeConfig.saturation]}
                onValueChange={([value]) => {
                  updateThemeConfig({ saturation: value })
                }}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">主题明暗</Label>
                  <p className="text-xs text-muted-foreground">
                    0=深色主题，100=浅色主题
                  </p>
                </div>
                <span className="text-sm text-muted-foreground font-mono">
                  {themeConfig.lightness}%
                </span>
              </div>
              <Slider
                value={[themeConfig.lightness]}
                onValueChange={([value]) => {
                  updateThemeConfig({ lightness: value })
                }}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">圆角半径</Label>
                  <p className="text-xs text-muted-foreground">
                    控制组件的圆角大小
                  </p>
                </div>
                <span className="text-sm text-muted-foreground font-mono">
                  {themeConfig.radius}rem
                </span>
              </div>
              <Slider
                value={[themeConfig.radius]}
                onValueChange={([value]) => {
                  updateThemeConfig({ radius: value })
                }}
                min={0}
                max={1.5}
                step={0.1}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* 主题控制区域 */}
        <div className="space-y-6">
          {/* 明暗主题切换 */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label className="text-sm font-medium">明暗主题预览</Label>
              <p className="text-xs text-muted-foreground">
                切换浅色/深色模式预览主题效果
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Sun className="h-4 w-4" />
              <Switch
                checked={theme === 'dark'}
                onCheckedChange={checked =>
                  setTheme(checked ? 'dark' : 'light')
                }
              />
              <Moon className="h-4 w-4" />
            </div>
          </div>

          {/* 自定义主题开关 */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label
                htmlFor="theme-toggle"
                className="text-sm font-medium">
                启用自定义主题
              </Label>
              <p className="text-xs text-muted-foreground">
                应用您的自定义颜色到整个应用
              </p>
            </div>
            <Switch
              id="theme-toggle"
              checked={isCustomThemeActive}
              onCheckedChange={toggleCustomTheme}
            />
          </div>

          <div className="flex gap-3">
            <Button
              onClick={applyTheme}
              className="flex-1"
              disabled={!isCustomThemeActive}>
              应用主题
            </Button>
            <Button
              onClick={resetTheme}
              variant="outline">
              重置
            </Button>
            <ThemeCSSExportDialog />
          </div>

          {/* 颜色信息 */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-3">
            <h4 className="text-sm font-medium">颜色信息</h4>
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="space-y-1">
                <span className="text-muted-foreground">色相</span>
                <div className="font-mono">{themeConfig.hue}°</div>
              </div>
              <div className="space-y-1">
                <span className="text-muted-foreground">饱和度</span>
                <div className="font-mono">{themeConfig.saturation}%</div>
              </div>
              <div className="space-y-1">
                <span className="text-muted-foreground">亮度</span>
                <div className="font-mono">{themeConfig.lightness}%</div>
              </div>
              <div className="space-y-1">
                <span className="text-muted-foreground">Hex</span>
                <div className="font-mono">{getCurrentHexColor()}</div>
              </div>
            </div>
            <div className="space-y-1">
              <span className="text-muted-foreground">OKLCH</span>
              <div className="font-mono text-xs">
                {getCurrentOklchValues()
                  .map(v => v.toFixed(3))
                  .join(' ')}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
