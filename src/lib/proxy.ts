// 代理配置 - 仅在开发环境中使用

// import { bootstrap } from 'global-agent';

// if (process.env.NODE_ENV === 'development' && !process.env.PLAYWRIGHT) {
//   // 检查是否设置了代理环境变量
//   if (process.env.HTTP_PROXY || process.env.HTTPS_PROXY) {
//     console.log('🌐 启用代理配置:', {
//       HTTP_PROXY: process.env.HTTP_PROXY,
//       HTTPS_PROXY: process.env.HTTPS_PROXY,
//     });

//     // 启用全局代理
//     bootstrap();
//   }
// }
