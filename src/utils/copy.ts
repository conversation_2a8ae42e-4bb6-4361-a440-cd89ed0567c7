/**
 * 复制工具函数
 * 提供在不能使用hooks的地方进行复制的功能
 */

/**
 * 复制文本到剪贴板
 * 优先使用现代的 Clipboard API，回退到传统方法
 */
export async function copyTextToClipboard(text: string): Promise<boolean> {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      return true
    }

    // 回退到传统方法
    return fallbackCopyTextToClipboard(text)
  } catch (error) {
    console.error('Failed to copy using Clipboard API:', error)
    // 如果现代API失败，尝试回退方法
    return fallbackCopyTextToClipboard(text)
  }
}

/**
 * 传统的复制方法（回退方案）
 */
function fallbackCopyTextToClipboard(text: string): boolean {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text

    // 设置样式使其不可见
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    return successful
  } catch (error) {
    console.error('Fallback copy method failed:', error)
    return false
  }
}

/**
 * 复制图片到剪贴板
 * 仅适用于支持 ClipboardItem 的浏览器
 */
export async function copyImageToClipboard(
  imageData: string | Blob
): Promise<boolean> {
  try {
    if (!navigator.clipboard || !window.ClipboardItem) {
      console.warn('Clipboard API or ClipboardItem not supported')
      return false
    }

    let blob: Blob

    if (typeof imageData === 'string') {
      // 如果是base64字符串，转换为blob
      const response = await fetch(`data:image/png;base64,${imageData}`)
      blob = await response.blob()
    } else {
      blob = imageData
    }

    const clipboardItem = new ClipboardItem({ [blob.type]: blob })
    await navigator.clipboard.write([clipboardItem])

    return true
  } catch (error) {
    console.error('Failed to copy image:', error)
    return false
  }
}

/**
 * 检查是否支持剪贴板功能
 */
export function isClipboardSupported(): boolean {
  return !!(navigator.clipboard || document.execCommand)
}

/**
 * 检查是否支持现代剪贴板API
 */
export function isModernClipboardSupported(): boolean {
  return !!(navigator.clipboard && navigator.clipboard.writeText)
}

/**
 * 检查是否支持图片复制
 */
export function isImageClipboardSupported(): boolean {
  return !!(navigator.clipboard && window.ClipboardItem)
}
